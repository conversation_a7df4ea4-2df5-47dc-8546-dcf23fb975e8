/**
 * MIPRO V2 自动化 Prompt 优化器
 *
 * 基于 DSPy 论文中的 MIPRO V2 算法实现的 TypeScript 版本
 *
 * 主要功能：
 * 1. Bootstrap Demonstrations - 自动生成高质量的 few-shot 示例
 * 2. Propose Instructions - 使用 LLM 生成候选指令
 * 3. Bayesian Optimization - 使用 TPE 算法优化 prompt 参数
 *
 * 使用方法：
 * ```typescript
 * import { MIPROv2Optimizer, createSampleDataset } from './mipro';
 *
 * const optimizer = new MIPROv2Optimizer({
 *   metric: yourMetricFunction,
 *   promptModel: yourLLM,
 *   taskModel: yourLLM,
 *   numTrials: 20
 * });
 *
 * const result = await optimizer.optimize(yourProgram, yourTrainset);
 * console.log(result.bestPrompts);
 * ```
 */

// 核心类型定义
import { LLM } from '../../llm/LLM'
import { semanticSimilarityMetric } from './example_program'
import { MIPROv2Optimizer } from './mipro_optimizer'

export * from './types'

// 主要优化器
export { MIPROv2Optimizer } from './mipro_optimizer'

// TPE 贝叶斯优化器
export { TPEOptimizer } from './tpe'

// Prompt 格式化工具
export { PromptFormatter } from './prompt_formatter'

// 示例程序和指标
export {
  QuestionAnsweringProgram,
  exactMatchMetric,
  containsMetric,
  semanticSimilarityMetric,
  createSampleDataset
} from './example_program'

/**
 * 快速开始函数 - 使用默认配置优化程序
 */
export async function quickOptimize(
  program: any,
  trainset: any[],
  options: {
    llm?: any;
    metric?: any;
    numTrials?: number;
  } = {}
) {

  const defaultLLM = options.llm || new LLM({
    model: 'gpt-5-mini',
    temperature: 0.7,
    maxTokens: 500
  })

  const optimizer = new MIPROv2Optimizer({
    metric: options.metric || semanticSimilarityMetric,
    promptModel: defaultLLM,
    taskModel: defaultLLM,
    numTrials: options.numTrials || 10,
    minibatchSize: Math.min(5, trainset.length),
    numCandidates: 3
  })

  return await optimizer.optimize(program, trainset)
}

/**
 * 创建一个简单的评估指标
 */
export function createSimpleMetric(
  extractAnswer: (prediction: any) => string,
  extractGroundTruth: (example: any) => string,
  compareFunction: (pred: string, truth: string) => number = (a, b) => a.toLowerCase().trim() === b.toLowerCase().trim() ? 1 : 0
) {
  return async (prediction: any, groundTruth: any): Promise<number> => {
    try {
      const predAnswer = extractAnswer(prediction)
      const trueAnswer = extractGroundTruth(groundTruth)
      return compareFunction(predAnswer, trueAnswer)
    } catch (error) {
      console.warn('Error in metric evaluation:', error)
      return 0
    }
  }
}

/**
 * 版本信息
 */
export const VERSION = '1.0.0'
export const ALGORITHM = 'MIPRO V2'
export const DESCRIPTION = 'Multi-prompt Instruction Proposal and Refinement Optimizer V2'
