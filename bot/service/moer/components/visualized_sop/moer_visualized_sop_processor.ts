import { LLM } from '../../../../lib/ai/llm/LLM'
import { LLMNode } from '../flow/nodes/llm'
import { getState } from '../flow/schedule/task/baseTask'
import { HandleActionOption, sendMsg, VisualizedSopProcessor } from './visualized_sop_processor'
import { ContentDynamicPrompt } from './visualized_sop_type'
import { actionCustomMap, conditionJudgeMap, linkSourceVariableTagMap, textVariableMap } from './visualized_sop_variable'

export class MoerVisualizedSopProcessor extends VisualizedSopProcessor {
  async handleActionDynamicPrompt(chatId: string, userId: string, action: ContentDynamicPrompt, opt: HandleActionOption): Promise<void> {
    const state = await getState(chatId, userId)
    const res = await LLMNode.getReplyContent({
      useRAG: action.includeRAG,
      temperature: 0.8,
      recallMemory: action.includeMemory,
      chatHistoryRounds: action.chatHistoryRounds,
      promptName: `sop_${action.description}`,
      noStagePrompt:!action.includeUserBehavior,
      noUserSlots:!action.includeUserSlots,
      customPrompt: '# 动态SOP\n你需要根据任务与客户画像来生成SOP话术',
      dynamicPrompt: action.dynamicPrompt,
      state: state
    }, new LLM({
      meta: {
        chat_id: chatId,
        round_id: state.round_id,
        promptName: `sop_${action.description}`,
      },
      model: 'gpt-5-mini',
      temperature: 0.8,
    }))
    if (action.noSplit) {
      await sendMsg(userId, chatId, res, action.description, opt.force, state.round_id, opt.sop_id)
    } else {
      const splitSentence = LLMNode.splitIntoSentencesWithMaxSentences(res, 2)
      await sendMsg(userId, chatId, splitSentence, action.description, opt.force, state.round_id, opt.sop_id)
    }
  }
  getActionCustomMap(): Record<string, (params: { chatId: string; userId: string }) => Promise<void>> {
    return actionCustomMap
  }
  getConditionJudgeMap(): Record<string, ((params: { chatId: string; userId: string }) => Promise<boolean>)> {
    return conditionJudgeMap
  }
  getLinkSourceVariableTagMap(): Record<string, (params: { chatId: string; userId: string }) => Promise<string>> {
    return linkSourceVariableTagMap
  }
  getTextVariableMap(): Record<string, (params: { chatId: string; userId: string }) => Promise<string>> {
    return textVariableMap
  }
}